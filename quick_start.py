#!/usr/bin/env python3
"""
闲鱼自动回复系统 - 快速体验版
最小化依赖，快速启动
"""

import os
import sys
import subprocess
import webbrowser
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，需要Python 3.8+")
        print("请访问 https://www.python.org/downloads/ 下载最新版本")
        return False
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def install_basic_deps():
    """安装基础依赖"""
    basic_deps = [
        "fastapi>=0.100.0",
        "uvicorn[standard]>=0.20.0", 
        "pydantic>=2.0.0",
        "loguru>=0.7.0",
        "aiohttp>=3.8.0",
        "PyYAML>=6.0.0"
    ]
    
    print("📦 正在安装基础依赖...")
    for dep in basic_deps:
        try:
            print(f"安装 {dep}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", dep,
                "-i", "https://pypi.tuna.tsinghua.edu.cn/simple/"
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        except subprocess.CalledProcessError:
            print(f"⚠️ {dep} 安装失败，跳过...")
    
    print("✅ 基础依赖安装完成")

def create_minimal_config():
    """创建最小化配置"""
    config_content = """
# 最小化配置文件
WEBSOCKET_URL: wss://wss-goofish.dingtalk.com/
HEARTBEAT_INTERVAL: 15
HEARTBEAT_TIMEOUT: 5
TOKEN_REFRESH_INTERVAL: 3600
TOKEN_RETRY_INTERVAL: 300
MESSAGE_EXPIRE_TIME: 300000

AUTO_REPLY:
  enabled: true
  default_message: "您好！欢迎咨询，有什么可以帮助您的吗？"
  api:
    enabled: false
    host: 0.0.0.0
    port: 8080

LOG_CONFIG:
  level: INFO
  format: '<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level}</level> | {message}'

COOKIES:
  value: ''
  last_update_time: ''

API_ENDPOINTS:
  login_check: https://passport.goofish.com/newlogin/hasLogin.do
  message_headinfo: https://h5api.m.goofish.com/h5/mtop.idle.trade.pc.message.headinfo/1.0/
  token: https://h5api.m.goofish.com/h5/mtop.taobao.idlemessage.pc.login.token/1.0/

DEFAULT_HEADERS:
  accept: application/json
  accept-language: zh-CN,zh;q=0.9
  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36

WEBSOCKET_HEADERS:
  Accept-Language: zh-CN,zh;q=0.9
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36

APP_CONFIG:
  api_version: '1.0'
  app_key: 444e9908a51d1cb236a27862abc769c9
  platform: web
"""
    
    with open("global_config.yml", "w", encoding="utf-8") as f:
        f.write(config_content.strip())
    print("✅ 配置文件已创建")

def start_server():
    """启动服务器"""
    print("🚀 正在启动服务器...")
    print("📱 启动完成后将自动打开浏览器")
    print("🌐 访问地址: http://localhost:8080")
    print("⏹️  按 Ctrl+C 停止服务")
    print("-" * 50)
    
    try:
        # 尝试启动主程序
        if os.path.exists("Start.py"):
            subprocess.run([sys.executable, "Start.py"])
        else:
            print("❌ 未找到Start.py文件")
            print("请确保在项目根目录下运行此脚本")
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    """主函数"""
    print("🐟 闲鱼自动回复系统 - 快速启动工具")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 检查是否在项目目录
    if not os.path.exists("requirements.txt"):
        print("❌ 请在项目根目录下运行此脚本")
        print("当前目录应包含 requirements.txt 文件")
        return
    
    # 安装依赖
    install_basic_deps()
    
    # 创建配置文件
    if not os.path.exists("global_config.yml"):
        create_minimal_config()
    
    # 启动服务
    start_server()

if __name__ == "__main__":
    main()
